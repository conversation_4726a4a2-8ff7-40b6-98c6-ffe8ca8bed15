package com.developer.faker.Adapter;

import android.content.Intent;
import android.net.Uri;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Data.RecentCallData;
import com.developer.faker.R;
import com.developer.faker.Utils.Const;
import java.util.ArrayList;

/**
 * 最近通话记录列表适配器
 * 用于显示最近的通话记录和短信记录
 */
public class RecentCallListAdapter extends ArrayAdapter<RecentCallData> {
    BaseActivity activity;      // 活动引用
    final Integer[] icons;      // 图标数组

    /**
     * 构造函数
     * @param baseActivity 基础活动
     * @param i 布局资源ID
     * @param arrayList 数据列表
     */
    public RecentCallListAdapter(BaseActivity baseActivity, int i, ArrayList<RecentCallData> arrayList) {
        super(baseActivity, i, arrayList);
        // 初始化图标数组：电话图标和邮件图标
        this.icons = new Integer[]{Integer.valueOf(R.mipmap.call), Integer.valueOf(R.mipmap.email)};
        this.activity = baseActivity;
    }

    /**
     * 获取列表项视图
     * @param i 位置
     * @param view 复用的视图
     * @param viewGroup 父视图组
     * @return 列表项视图
     */
    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);  // 创建新视图
        }
        bindView(i, view);  // 绑定数据到视图
        return view;
    }

    /**
     * 创建新的列表项视图
     * @param viewGroup 父视图组
     * @return 新创建的视图
     */
    private View newView(ViewGroup viewGroup) {
        return this.activity.getLayoutInflater().inflate(R.layout.adapter_recentcalllist, viewGroup, false);
    }

    /**
     * 绑定数据到视图
     * @param i 数据位置
     * @param view 要绑定的视图
     */
    private void bindView(int i, View view) {
        final RecentCallData item = getItem(i);  // 获取数据项

        // 设置通话按钮点击监听器
        view.findViewById(R.id.btnCall).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view2) {
                if (item.callType == Const.CALL_TYPE_PHONE.intValue()) {
                    // 如果是电话类型，启动拨号Intent
                    RecentCallListAdapter.this.getContext().startActivity(
                            new Intent("android.intent.action.CALL", Uri.parse("tel:" + item.phonenumber)));
                    return;
                }
                // 如果是短信类型，发送短信Intent
                RecentCallListAdapter.this.sendSmsIntent(item.phonenumber);
            }
        });

        // 获取UI组件
        ImageView imageView = (ImageView) view.findViewById(R.id.imgPhone);      // 图标
        TextView textView = (TextView) view.findViewById(R.id.txtPhoneNumber);   // 电话号码
        TextView textView2 = (TextView) view.findViewById(R.id.txtDate);         // 日期
        TextView textView3 = (TextView) view.findViewById(R.id.txtMemo);         // 备注

        // 设置数据到UI组件
        imageView.setImageResource(this.icons[item.callType].intValue());  // 设置图标
        textView.setText(item.phonenumber);   // 设置电话号码
        textView2.setText(item.date);         // 设置日期
        textView3.setText(item.memo);         // 设置备注
        view.setTag(item);                    // 设置标签
    }

    public void sendSmsIntent(String str) {
        try {
            Intent intent = new Intent("android.intent.action.SENDTO", Uri.parse("sms:" + str));
            intent.putExtra("sms_body", "");
            getContext().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}